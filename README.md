# EnglishAudio 插件

这是一个用于获取英语音频的插件，可以根据模块编号获取对应的英语音频文件。

## 功能特点

- 通过简单的命令获取英语音频
- 支持自动补全模块编号
- 提供完整的模块列表查询
- 自动下载并转换为语音消息发送

## 使用方法

1. 发送 `英语 模块编号` 获取对应英语音频，例如：`英语 M1U1`
2. 发送 `英语` 获取所有可用的模块列表
3. 也可以简化输入，例如：
   - 单元音频：`英语 1` 会自动匹配 M1U1 或 M1U2
   - 单词音频：`英语 1单词` 会匹配 M1W
   - 听力音频：`英语听力 1-1` 会匹配 M1U1L
   - 测试卷：`英语测试 1` 会匹配 M1T

## 安装方法

1. 将整个 `EnglishAudio` 文件夹复制到 `plugins` 目录下
2. 重启应用程序

## 配置文件

插件使用 `config.json` 文件存储音频链接映射，格式为：

```json
{
  "模块编号": "音频链接",
  ...
}
```

您可以根据需要修改 `config.json` 文件，添加或更新音频链接。

## 当前可用模块

### 单元音频
- M1U1, M1U2
- M2U1, M2U2
- M3U1, M3U2
- M4U1, M4U2
- M5U1, M5U2
- M6U1, M6U2
- M7U1, M7U2
- M8U1, M8U2
- M9U1, M9U2
- M10U1, M10U2

### 单词音频
- M1W (Module1 单词)
- M2W (Module2 单词)
- M3W (Module3 单词)
- M4W (Module4 单词)
- M5W (Module5 单词)
- M6W (Module6 单词)
- M7W (Module7 单词)
- M8W (Module8 单词)
- M9W (Module9 单词)
- M10W (Module10 单词)

### 听力音频
- M1U1L, M1U2L (Module1 听力)
- M2U1L, M2U2L (Module2 听力)
- M3U1L, M3U2L (Module3 听力)
- M4U1L, M4U2L (Module4 听力)
- M5U1L, M5U2L (Module5 听力)
- M6U1L, M6U2L (Module6 听力)
- M7U1L, M7U2L (Module7 听力)
- M8U1L, M8U2L (Module8 听力)
- M9U1L, M9U2L (Module9 听力)
- M10U1L, M10U2L (Module10 听力)

### 测试卷音频
- M1T 到 M10T (Module1-10 综合测试卷)
- MT1 (期中综合测试卷)
- MT2 (期末综合测试卷)

### 其他音频
- Proper nouns (专有名词)
- Words in songs and chants (歌曲和朗诵中的单词) 