#!/usr/bin/env python3
# encoding:utf-8


import requests
import os
import time
import random
import tomllib
from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase


class EnglishAudio(PluginBase):
    description = "英语音频插件"
    author = "AI Assistant"
    version = "1.0.0"

    def __init__(self):
        super().__init__()

        # 获取配置文件路径
        config_path = os.path.join(os.path.dirname(__file__), "config.toml")

        try:
            with open(config_path, "rb") as f:
                config = tomllib.load(f)

            # 读取基本配置
            basic_config = config.get("basic", {})
            self.enable = basic_config.get("enable", True)  # 读取插件开关

            # 读取音频配置
            audio_config = config.get("audio", {})
            self.audio_map = audio_config.get("modules", {})

            logger.info(f"[EnglishAudio] 插件已初始化，包含 {len(self.audio_map)} 个音频条目")

        except Exception as e:
            logger.error(f"[EnglishAudio] 加载配置文件失败: {str(e)}")
            self.enable = False  # 如果加载失败，禁用插件
            self.audio_map = {}



    def download_audio(self, audio_url, module_name):
        """
        下载音频文件并返回文件路径
        :param audio_url: 音频文件URL
        :param module_name: 模块名称（用于文件名）
        :return: 音频文件保存路径或None（如果下载失败）
        """
        try:
            # 检查URL是否有效
            if not audio_url or not audio_url.startswith('http'):
                logger.error(f"[EnglishAudio] 无效的音频URL: {audio_url}")
                return None

            # 发送GET请求下载文件，添加超时和重试机制
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            for retry in range(3):  # 最多重试3次
                try:
                    response = requests.get(audio_url, stream=True, headers=headers, timeout=30)
                    response.raise_for_status()  # 检查响应状态
                    break
                except requests.RequestException as e:
                    if retry == 2:  # 最后一次重试
                        logger.error(f"[EnglishAudio] 下载音频文件失败，重试次数已用完: {e}")
                        return None
                    logger.warning(f"[EnglishAudio] 下载重试 {retry + 1}/3: {e}")
                    time.sleep(1)  # 等待1秒后重试

            # 使用系统临时目录
            import tempfile
            tmp_dir = tempfile.gettempdir()

            # 生成唯一的文件名，包含时间戳和随机字符串
            timestamp = int(time.time())
            random_str = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz', k=6))
            audio_name = f"eng_{module_name}_{timestamp}_{random_str}.mp3"
            audio_path = os.path.join(tmp_dir, audio_name)

            # 保存文件，使用块写入以节省内存
            total_size = 0
            with open(audio_path, "wb") as file:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        file.write(chunk)
                        total_size += len(chunk)

            # 验证文件大小
            if total_size == 0:
                logger.error("[EnglishAudio] 下载的文件大小为0")
                os.remove(audio_path)  # 删除空文件
                return None

            logger.info(f"[EnglishAudio] 音频下载完成: {audio_path}, 大小: {total_size/1024:.2f}KB")
            return audio_path

        except Exception as e:
            logger.error(f"[EnglishAudio] 下载音频文件时出错: {e}")
            # 如果文件已创建，清理它
            if 'audio_path' in locals() and os.path.exists(audio_path):
                try:
                    os.remove(audio_path)
                except Exception as clean_error:
                    logger.error(f"[EnglishAudio] 清理失败的下载文件时出错: {clean_error}")
            return None

    @on_text_message(priority=50)
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return True  # 插件未启用，允许后续插件处理

        content = message["Content"]

        # 检查是否是英语音频请求
        if content.lower().startswith("英语 ") or content.lower().startswith("英语") or content.lower().startswith("英语听力") or content.lower().startswith("英语测试"):
            # 提取模块编号
            module_code = content.lower().replace("英语听力", "").replace("英语测试", "").replace("英语", "").strip()

            # 如果没有提供模块编号，返回可用的模块列表
            if not module_code:
                available_modules = ", ".join(sorted(self.audio_map.keys()))
                await bot.send_text_message(
                    message["FromWxid"],
                    f"请指定模块编号，可用的模块有: {available_modules}"
                )
                return False  # 阻止后续插件处理

            # 是否为听力请求
            is_listening_request = "听力" in content.lower()

            # 创建不区分大小写的音频映射字典用于查找
            case_insensitive_map = {k.lower(): (k, v) for k, v in self.audio_map.items()}

            # 查找对应的音频URL
            audio_url = None

            # 首先尝试直接匹配（忽略大小写）
            if module_code.lower() in case_insensitive_map:
                original_key, audio_url_value = case_insensitive_map[module_code.lower()]
                # 对于听力请求，确保匹配到的是带L后缀的
                if not is_listening_request or (is_listening_request and original_key.endswith('L')):
                    audio_url = audio_url_value
                    module_code = original_key
                    logger.info(f"[EnglishAudio] 直接匹配音频: {module_code}")

            # 如果没有找到合适的匹配，尝试智能匹配
            if not audio_url:
                # 处理听力音频请求
                if is_listening_request:
                    # 处理形如 "1-1" 或 "1.1" 的格式
                    parts = module_code.replace("-", ".").split(".")
                    if len(parts) == 2 and parts[0].isdigit() and parts[1].isdigit():
                        module_num, unit_num = parts
                        potential_code = f"m{module_num}u{unit_num}l"  # 转为小写进行比较
                        if potential_code in case_insensitive_map:
                            original_key, audio_url = case_insensitive_map[potential_code]
                            module_code = original_key
                            logger.info(f"[EnglishAudio] 匹配听力音频: {module_code}")
                    # 处理形如 "1" 的格式，尝试匹配 M1U1L 或 M1U2L
                    elif module_code.isdigit():
                        potential_codes = [f"m{module_code}u1l", f"m{module_code}u2l"]
                        for code in potential_codes:
                            if code in case_insensitive_map:
                                original_key, audio_url = case_insensitive_map[code]
                                module_code = original_key
                                logger.info(f"[EnglishAudio] 匹配听力音频: {module_code}")
                                break
                    # 处理形如 "M1U1" 的格式
                    else:
                        # 如果输入的是标准模块格式（如M1U1），自动添加L后缀
                        if module_code.lower().startswith('m') and 'u' in module_code.lower():
                            potential_code = f"{module_code.lower()}l"
                            if potential_code in case_insensitive_map:
                                original_key, audio_url = case_insensitive_map[potential_code]
                                module_code = original_key
                                logger.info(f"[EnglishAudio] 匹配听力音频: {module_code}")
                        # 如果输入不带L后缀，则尝试添加
                        potential_code = f"{module_code.lower()}l"
                        if potential_code in case_insensitive_map:
                            original_key, audio_url = case_insensitive_map[potential_code]
                            module_code = original_key
                            logger.info(f"[EnglishAudio] 匹配听力音频: {module_code}")
                # 处理测试卷请求
                elif "测试" in content.lower() or "test" in content.lower():
                    # 处理期中期末测试卷
                    if "期中" in module_code.lower() or "mid" in module_code.lower():
                        if "mt1".lower() in case_insensitive_map:
                            original_key, audio_url = case_insensitive_map["mt1"]
                            module_code = original_key
                            logger.info(f"[EnglishAudio] 匹配期中测试卷音频")
                    elif "期末" in module_code.lower() or "final" in module_code.lower():
                        if "mt2".lower() in case_insensitive_map:
                            original_key, audio_url = case_insensitive_map["mt2"]
                            module_code = original_key
                            logger.info(f"[EnglishAudio] 匹配期末测试卷音频")
                    # 处理模块测试卷
                    elif module_code.isdigit():
                        potential_code = f"m{module_code}t"
                        if potential_code in case_insensitive_map:
                            original_key, audio_url = case_insensitive_map[potential_code]
                            module_code = original_key
                            logger.info(f"[EnglishAudio] 匹配模块测试卷音频: {module_code}")
                    # 处理形如 "M1" 的格式
                    elif module_code.lower().startswith('m') and module_code[1:].isdigit():
                        potential_code = f"{module_code.lower()}t"
                        if potential_code in case_insensitive_map:
                            original_key, audio_url = case_insensitive_map[potential_code]
                            module_code = original_key
                            logger.info(f"[EnglishAudio] 匹配模块测试卷音频: {module_code}")
                # 处理普通请求（非听力非测试）
                else:
                    if module_code.isdigit():
                        # 尝试匹配 M{module_code}U1, M{module_code}U2, M{module_code}W
                        potential_codes = [
                            f"m{module_code}u1",
                            f"m{module_code}u2",
                            f"m{module_code}w"
                        ]
                        for code in potential_codes:
                            if code in case_insensitive_map:
                                original_key, audio_url = case_insensitive_map[code]
                                module_code = original_key
                                logger.info(f"[EnglishAudio] 自动补全模块编号: {module_code}")
                                break
                    # 处理 "单词" 或 "words" 关键词
                    elif "单词" in module_code.lower() or "words" in module_code.lower():
                        # 提取数字部分
                        for part in module_code.split():
                            if part.isdigit():
                                potential_code = f"m{part}w"
                                if potential_code in case_insensitive_map:
                                    original_key, audio_url = case_insensitive_map[potential_code]
                                    module_code = original_key
                                    logger.info(f"[EnglishAudio] 匹配单词音频: {module_code}")
                                    break
                    # 处理特殊关键词
                    elif "专有名词" in module_code.lower() or "proper" in module_code.lower():
                        proper_nouns_key = "proper nouns"
                        if proper_nouns_key in case_insensitive_map:
                            original_key, audio_url = case_insensitive_map[proper_nouns_key]
                            module_code = original_key
                            logger.info(f"[EnglishAudio] 匹配专有名词音频")
                    elif "歌曲" in module_code.lower() or "朗诵" in module_code.lower() or "songs" in module_code.lower() or "chants" in module_code.lower():
                        songs_key = "words in songs and chants"
                        if songs_key in case_insensitive_map:
                            original_key, audio_url = case_insensitive_map[songs_key]
                            module_code = original_key
                            logger.info(f"[EnglishAudio] 匹配歌曲和朗诵单词音频")

            if audio_url:
                logger.info(f"[EnglishAudio] 找到模块 {module_code} 的音频: {audio_url}")

                # 下载音频文件
                audio_path = self.download_audio(audio_url, module_code)

                if audio_path:
                    # 发送语音消息
                    try:
                        # 读取音频文件数据
                        with open(audio_path, "rb") as audio_file:
                            audio_data = audio_file.read()

                        logger.info(f"[EnglishAudio] 开始发送语音消息: 接收者={message['FromWxid']}, 文件大小={len(audio_data)}字节")

                        # 发送语音消息 - 使用正确的参数格式
                        result = await bot.send_voice_message(
                            message["FromWxid"],
                            voice=audio_data,
                            format="mp3"
                        )

                        logger.info(f"[EnglishAudio] 语音发送结果: {result}")
                        logger.info(f"[EnglishAudio] 成功发送音频: {module_code}")

                        # 清理临时文件
                        try:
                            os.remove(audio_path)
                            logger.debug(f"[EnglishAudio] 已清理临时文件: {audio_path}")
                        except Exception as cleanup_error:
                            logger.warning(f"[EnglishAudio] 清理临时文件失败: {cleanup_error}")

                    except Exception as send_error:
                        logger.error(f"[EnglishAudio] 发送语音消息失败: {send_error}")
                        await bot.send_text_message(
                            message["FromWxid"],
                            f"音频下载成功，但发送失败。请稍后重试。错误: {str(send_error)}"
                        )

                        # 清理临时文件
                        try:
                            if os.path.exists(audio_path):
                                os.remove(audio_path)
                        except Exception:
                            pass
                else:
                    await bot.send_text_message(
                        message["FromWxid"],
                        "下载音频失败，请稍后重试"
                    )
            else:
                available_modules = ", ".join(sorted(self.audio_map.keys()))
                if is_listening_request:
                    error_msg = f"未找到模块 {module_code} 的听力音频，注意听力音频必须带L后缀。可用的模块有: {available_modules}"
                else:
                    error_msg = f"未找到模块 {module_code} 的音频，可用的模块有: {available_modules}"

                await bot.send_text_message(message["FromWxid"], error_msg)

            return False  # 阻止后续插件处理

        return True  # 允许后续插件处理

